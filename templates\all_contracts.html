<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Contracts - GigGenius</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2d3748;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            color: #6b7280;
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card .icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            font-size: 20px;
            color: white;
        }

        .stat-card.active .icon {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .stat-card.completed .icon {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        .stat-card.pending .icon {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .stat-card.cancelled .icon {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        .stat-card h3 {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .stat-card p {
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
        }

        .filters-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 5px;
        }

        .filter-select, .filter-input {
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .filter-select:focus, .filter-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .contracts-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f3f4f6;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
        }

        .contract-card {
            border: 1px solid #e5e7eb;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .contract-card:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }

        .contract-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .contract-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .contract-id {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.active {
            background: #d1fae5;
            color: #065f46;
        }

        .status-badge.completed {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-badge.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-badge.cancelled {
            background: #fee2e2;
            color: #991b1b;
        }

        .contract-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
            margin-bottom: 3px;
        }

        .detail-value {
            font-size: 14px;
            color: #1f2937;
            font-weight: 600;
        }

        .contract-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .no-contracts {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }

        .no-contracts i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #d1d5db;
        }

        .no-contracts h3 {
            font-size: 20px;
            margin-bottom: 10px;
            color: #374151;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 24px;
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .contract-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .contract-details {
                grid-template-columns: 1fr;
            }

            .contract-actions {
                justify-content: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <h1><i class="fas fa-file-contract"></i> All Contracts</h1>
            <p>Manage and track all your contracts in one place</p>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card active">
                <div class="icon">
                    <i class="fas fa-play-circle"></i>
                </div>
                <h3>12</h3>
                <p>Active Contracts</p>
            </div>
            <div class="stat-card completed">
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3>45</h3>
                <p>Completed Contracts</p>
            </div>
            <div class="stat-card pending">
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3>8</h3>
                <p>Pending Approval</p>
            </div>
            <div class="stat-card cancelled">
                <div class="icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <h3>3</h3>
                <p>Cancelled</p>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <div class="filters-grid">
                <div class="filter-group">
                    <label for="statusFilter">Status</label>
                    <select id="statusFilter" class="filter-select">
                        <option value="">All Statuses</option>
                        <option value="active">Active</option>
                        <option value="completed">Completed</option>
                        <option value="pending">Pending</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="clientFilter">Client</label>
                    <select id="clientFilter" class="filter-select">
                        <option value="">All Clients</option>
                        <option value="client1">TechCorp Inc.</option>
                        <option value="client2">Design Studio</option>
                        <option value="client3">StartupXYZ</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="dateFilter">Date Range</label>
                    <input type="date" id="dateFilter" class="filter-input">
                </div>
                <div class="filter-group">
                    <label for="searchFilter">Search</label>
                    <input type="text" id="searchFilter" class="filter-input" placeholder="Search contracts...">
                </div>
                <div class="filter-group">
                    <label>&nbsp;</label>
                    <button class="search-btn">
                        <i class="fas fa-search"></i> Filter
                    </button>
                </div>
            </div>
        </div>

        <!-- Contracts Section -->
        <div class="contracts-section">
            <div class="section-header">
                <h2 class="section-title">Contract List</h2>
            </div>

            <!-- Contract Cards -->
            <div class="contracts-list">
                <!-- Contract 1 - Active -->
                <div class="contract-card">
                    <div class="contract-header">
                        <div>
                            <div class="contract-title">Website Development Project</div>
                            <div class="contract-id">Contract #CT-2024-001</div>
                        </div>
                        <span class="status-badge active">Active</span>
                    </div>
                    <div class="contract-details">
                        <div class="detail-item">
                            <span class="detail-label">Client</span>
                            <span class="detail-value">TechCorp Inc.</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Value</span>
                            <span class="detail-value">$5,500</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Start Date</span>
                            <span class="detail-value">Jan 15, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">End Date</span>
                            <span class="detail-value">Mar 15, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Progress</span>
                            <span class="detail-value">65%</span>
                        </div>
                    </div>
                    <div class="contract-actions">
                        <button class="action-btn btn-primary">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-download"></i> Download
                        </button>
                    </div>
                </div>

                <!-- Contract 2 - Completed -->
                <div class="contract-card">
                    <div class="contract-header">
                        <div>
                            <div class="contract-title">Mobile App UI Design</div>
                            <div class="contract-id">Contract #CT-2024-002</div>
                        </div>
                        <span class="status-badge completed">Completed</span>
                    </div>
                    <div class="contract-details">
                        <div class="detail-item">
                            <span class="detail-label">Client</span>
                            <span class="detail-value">Design Studio</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Value</span>
                            <span class="detail-value">$3,200</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Start Date</span>
                            <span class="detail-value">Dec 1, 2023</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">End Date</span>
                            <span class="detail-value">Jan 10, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Progress</span>
                            <span class="detail-value">100%</span>
                        </div>
                    </div>
                    <div class="contract-actions">
                        <button class="action-btn btn-primary">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-download"></i> Download
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-star"></i> Rate
                        </button>
                    </div>
                </div>

                <!-- Contract 3 - Pending -->
                <div class="contract-card">
                    <div class="contract-header">
                        <div>
                            <div class="contract-title">Content Writing Package</div>
                            <div class="contract-id">Contract #CT-2024-003</div>
                        </div>
                        <span class="status-badge pending">Pending</span>
                    </div>
                    <div class="contract-details">
                        <div class="detail-item">
                            <span class="detail-label">Client</span>
                            <span class="detail-value">StartupXYZ</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Value</span>
                            <span class="detail-value">$1,800</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Start Date</span>
                            <span class="detail-value">Feb 1, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">End Date</span>
                            <span class="detail-value">Feb 28, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Progress</span>
                            <span class="detail-value">0%</span>
                        </div>
                    </div>
                    <div class="contract-actions">
                        <button class="action-btn btn-primary">
                            <i class="fas fa-check"></i> Accept
                        </button>
                        <button class="action-btn btn-danger">
                            <i class="fas fa-times"></i> Decline
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                    </div>
                </div>

                <!-- Contract 4 - Cancelled -->
                <div class="contract-card">
                    <div class="contract-header">
                        <div>
                            <div class="contract-title">E-commerce Platform Development</div>
                            <div class="contract-id">Contract #CT-2023-045</div>
                        </div>
                        <span class="status-badge cancelled">Cancelled</span>
                    </div>
                    <div class="contract-details">
                        <div class="detail-item">
                            <span class="detail-label">Client</span>
                            <span class="detail-value">RetailCorp</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Value</span>
                            <span class="detail-value">$8,500</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Start Date</span>
                            <span class="detail-value">Nov 15, 2023</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">End Date</span>
                            <span class="detail-value">Jan 30, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Progress</span>
                            <span class="detail-value">25%</span>
                        </div>
                    </div>
                    <div class="contract-actions">
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-archive"></i> Archive
                        </button>
                    </div>
                </div>
            </div>

            <!-- No Contracts State (hidden by default) -->
            <div class="no-contracts" style="display: none;">
                <i class="fas fa-file-contract"></i>
                <h3>No Contracts Found</h3>
                <p>You don't have any contracts yet. Start by applying to jobs or creating proposals.</p>
            </div>
        </div>
    </div>

    <script>
        // Filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const filterBtn = document.querySelector('.search-btn');
            const statusFilter = document.getElementById('statusFilter');
            const clientFilter = document.getElementById('clientFilter');
            const searchFilter = document.getElementById('searchFilter');
            const contractCards = document.querySelectorAll('.contract-card');

            function filterContracts() {
                const statusValue = statusFilter.value.toLowerCase();
                const clientValue = clientFilter.value.toLowerCase();
                const searchValue = searchFilter.value.toLowerCase();

                contractCards.forEach(card => {
                    const status = card.querySelector('.status-badge').textContent.toLowerCase();
                    const client = card.querySelector('.detail-value').textContent.toLowerCase();
                    const title = card.querySelector('.contract-title').textContent.toLowerCase();

                    const statusMatch = !statusValue || status.includes(statusValue);
                    const clientMatch = !clientValue || client.includes(clientValue);
                    const searchMatch = !searchValue || title.includes(searchValue) || client.includes(searchValue);

                    if (statusMatch && clientMatch && searchMatch) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            }

            filterBtn.addEventListener('click', filterContracts);
            searchFilter.addEventListener('input', filterContracts);
            statusFilter.addEventListener('change', filterContracts);
            clientFilter.addEventListener('change', filterContracts);

            // Action button handlers
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const action = this.textContent.trim();
                    const contractTitle = this.closest('.contract-card').querySelector('.contract-title').textContent;

                    // Add your action handlers here
                    console.log(`Action: ${action} on contract: ${contractTitle}`);

                    // Example: Show alert for demo
                    alert(`${action} clicked for: ${contractTitle}`);
                });
            });
        });
    </script>
</body>
</html>
