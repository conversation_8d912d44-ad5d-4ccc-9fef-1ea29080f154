<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Contracts - GigGenius</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --neutral-900: #1f2937;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 2000px;
            margin: 0;
            padding: 0;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 4.5rem;
            position: relative;
            width: 100%;
            max-width: 100%;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            color: var(--primary-blue);
        }

        .mobile-menu-btn:hover {
            color: var(--primary-pink);
            background-color: rgba(0, 74, 173, 0.05);
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
            padding-left: 0;
            flex: 1;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .logo img {
            width: 3.2rem;
            height: 3.2rem;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid rgba(0, 74, 173, 0.1);
        }

        .logo h1 {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin: 0;
            transition: color 0.3s ease;
        }

        .logo:hover h1 {
            color: var(--primary-blue);
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
            height: 100%;
            margin: 0;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1rem;
            font-weight: 500;
            position: relative;
            white-space: nowrap;
        }

        .nav-links a:hover, .nav-links a.active {
            color: var(--primary-pink);
        }

        .nav-links a:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-pink);
            transition: width 0.3s ease;
        }

        .nav-links a:hover:after, .nav-links a.active:after {
            width: 100%;
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
            margin: 0;
        }

        .nav-dropbtn {
            background: none;
            border: none;
            color: var(--primary-blue);
            padding: 0.5rem 1rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: color 0.3s ease;
            white-space: nowrap;
        }

        .nav-dropbtn:hover {
            color: var(--primary-pink);
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            z-index: 1000;
            border-radius: 8px;
            padding: 0.5rem 0;
            top: 100%;
            left: 0;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .nav-dropdown-content a {
            color: #333;
            padding: 0.75rem 1rem;
            text-decoration: none;
            display: block;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .nav-dropdown-content a:hover {
            background-color: rgba(0, 74, 173, 0.05);
            color: var(--primary-blue);
        }

        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }

        .right-section {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .search-container {
            position: relative;
        }

        .search-bar {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border-radius: 25px;
            padding: 0;
            width: 280px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .search-bar:focus-within {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
        }

        .search-dropdown {
            position: relative;
        }

        .search-dropdown-btn {
            background: none;
            border: none;
            padding: 0.6rem 1rem;
            font-size: 0.9rem;
            color: var(--primary-blue);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-right: 1px solid #e9ecef;
            white-space: nowrap;
        }

        .search-dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 1000;
            min-width: 150px;
        }

        .search-dropdown-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: background-color 0.3s ease;
        }

        .search-dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .search-bar input {
            flex: 1;
            border: none;
            background: none;
            padding: 0.6rem 1rem;
            font-size: 0.9rem;
            outline: none;
        }

        .search-bar .icon {
            padding: 0.6rem 1rem;
            color: #6c757d;
        }

        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .notification-icon {
            position: relative;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background-color 0.3s ease;
        }

        .notification-icon:hover {
            background-color: rgba(0, 74, 173, 0.05);
        }

        .notification-icon i {
            font-size: 1.2rem;
            color: var(--primary-blue);
        }

        .notification-icon span {
            position: absolute;
            top: 0;
            right: 0;
            background: var(--primary-pink);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .notification-dropdown {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
            z-index: 1000;
            width: 320px;
            max-height: 400px;
            overflow-y: auto;
        }

        .notification-header {
            padding: 18px 20px;
            border-bottom: 1px solid rgba(0,0,0,0.08);
            font-weight: 600;
            font-size: 1rem;
            color: var(--neutral-900);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-header-actions {
            font-size: 0.8rem;
            color: var(--primary-blue);
            cursor: pointer;
        }

        .profile-dropdown {
            position: relative;
        }

        .profile-button {
            cursor: pointer;
            border-radius: 50%;
            overflow: hidden;
            width: 40px;
            height: 40px;
            border: 2px solid rgba(0, 74, 173, 0.1);
            transition: border-color 0.3s ease;
        }

        .profile-button:hover {
            border-color: var(--primary-blue);
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-dropdown-content {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
            z-index: 1000;
            min-width: 200px;
            padding: 0.5rem 0;
        }

        .profile-dropdown:hover .profile-dropdown-content {
            display: block;
        }

        .profile-dropdown-content a {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: #333;
            text-decoration: none;
            transition: background-color 0.3s ease;
        }

        .profile-dropdown-content a:hover {
            background-color: #f8f9fa;
        }

        .dropdown-divider {
            height: 1px;
            background: #e9ecef;
            margin: 0.5rem 0;
        }

        .logout-option {
            color: #dc3545 !important;
        }

        /* Mobile Styles */
        .mobile-menu-header,
        .mobile-menu-content,
        .mobile-profile-section,
        .mobile-search-section,
        .mobile-profile-actions {
            display: none;
        }

        @media (max-width: 768px) {
            .navbar {
                padding: 0 0.5rem;
                height: 3.8rem;
            }

            .navbar-left {
                padding-left: 2.2rem;
            }

            .nav-links {
                display: none;
            }

            .mobile-menu-btn {
                display: flex;
                position: absolute;
                left: 0.4rem;
                top: 50%;
                transform: translateY(-50%);
                z-index: 1001;
            }

            .nav-links.active {
                display: flex;
                flex-direction: column;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
                z-index: 9999;
                padding: 0;
                margin: 0;
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
                scroll-behavior: smooth;
            }

            .mobile-menu-header,
            .mobile-menu-content,
            .mobile-profile-section,
            .mobile-search-section,
            .mobile-profile-actions {
                display: block;
            }

            .right-section {
                display: none !important;
            }
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .page-header h1 {
            color: #2d3748;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .page-header p {
            color: #6b7280;
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 0 20px 30px 20px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card .icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            font-size: 20px;
            color: white;
        }

        .stat-card.active .icon {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .stat-card.completed .icon {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        .stat-card.pending .icon {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .stat-card.cancelled .icon {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        .stat-card h3 {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .stat-card p {
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
        }

        .filters-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 0 20px 30px 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 5px;
        }

        .filter-select, .filter-input {
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .filter-select:focus, .filter-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .contracts-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin: 0 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f3f4f6;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
        }

        .contract-card {
            border: 1px solid #e5e7eb;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .contract-card:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }

        .contract-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .contract-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .contract-id {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.active {
            background: #d1fae5;
            color: #065f46;
        }

        .status-badge.completed {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-badge.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-badge.cancelled {
            background: #fee2e2;
            color: #991b1b;
        }

        .contract-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
            margin-bottom: 3px;
        }

        .detail-value {
            font-size: 14px;
            color: #1f2937;
            font-weight: 600;
        }

        .contract-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .no-contracts {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }

        .no-contracts i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #d1d5db;
        }

        .no-contracts h3 {
            font-size: 20px;
            margin-bottom: 10px;
            color: #374151;
        }

        @media (max-width: 768px) {
            .page-header {
                margin: 10px;
                padding: 20px;
            }

            .page-header h1 {
                font-size: 24px;
            }

            .stats-grid {
                margin: 0 10px 20px 10px;
            }

            .filters-section {
                margin: 0 10px 20px 10px;
            }

            .contracts-section {
                margin: 0 10px;
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .contract-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .contract-details {
                grid-template-columns: 1fr;
            }

            .contract-actions {
                justify-content: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Navbar -->
        <nav class="navbar">
            <div class="navbar-left">
                <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                    <div class="logo">
                        <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                        <h1>GigGenius</h1>
                    </div>
                </a>
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="nav-links" id="navLinks">
                    <!-- Mobile Menu Header (only visible on mobile) -->
                    <div class="mobile-menu-header">
                        <div class="logo">
                            <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                            <h1>GigGenius</h1>
                        </div>
                        <button class="mobile-close-btn" id="mobileCloseBtn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <!-- Mobile Menu Content Wrapper -->
                    <div class="mobile-menu-content">
                        <!-- Mobile Profile Section (only visible on mobile) - At Top -->
                        <div class="mobile-profile-section">
                            <div class="mobile-profile-info">
                                <img src="{{ url_for('api_profile_photo', user_type='client', user_id=session.get('user_id')) }}" alt="Profile Picture" class="mobile-profile-pic">
                                <div class="mobile-profile-details">
                                    <h3>{{ session.first_name }} {{ session.last_name }}</h3>
                                    <p>{{ session.email }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Mobile Search Section (only visible on mobile) -->
                        <div class="mobile-search-section">
                            <div class="mobile-search-container">
                                <div class="mobile-search-bar">
                                    <div class="search-dropdown">
                                        <button class="search-dropdown-btn" onclick="toggleMobileSearchDropdown()">
                                            <span id="mobileSearchDropdownText">Geniuses</span>
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                        <div class="search-dropdown-menu" id="mobileSearchDropdownMenu">
                                            <div class="search-dropdown-item" onclick="selectMobileSearchType('Geniuses')">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Geniuses</span>
                                            </div>
                                            <div class="search-dropdown-item" onclick="selectMobileSearchType('Jobs')">
                                                <i class="fas fa-briefcase"></i>
                                                <span>Jobs</span>
                                            </div>
                                            <div class="search-dropdown-item" onclick="selectMobileSearchType('Projects')">
                                                <i class="fas fa-project-diagram"></i>
                                                <span>Projects</span>
                                            </div>
                                        </div>
                                    </div>
                                    <input type="text" id="mobileSearchInput" placeholder="Search for Geniuses">
                                    <i class="fas fa-search icon"></i>
                                </div>
                            </div>
                        </div>
                    </div> <!-- End mobile-menu-content -->

                    <!-- Desktop Navigation Links (visible on desktop) -->
                    <a href="{{ url_for('page1') }}">Post a Gig</a>

                    <!-- Desktop Overview Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Overview
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('allgigpost') }}">All gig posts</a>
                            <a href="{{ url_for('all_contracts') }}">All contracts</a>
                            <a href="{{ url_for('landing_page') }}">Your Hires</a>
                        </div>
                    </div>

                    <!-- Desktop Manage Work Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Manage Work
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Timesheet</a>
                            <a href="{{ url_for('landing_page') }}">Invoices</a>
                        </div>
                    </div>

                    <!-- Desktop Reports Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Reports
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Weekly Summary</a>
                            <a href="{{ url_for('landing_page') }}">Transaction History</a>
                        </div>
                    </div>

                    <a href="{{ url_for('messages') }}">Messages</a>

                    <!-- Mobile Profile Actions (only visible on mobile) - At Bottom -->
                    <div class="mobile-profile-actions">
                        <a href="{{ url_for('landing_page') }}" class="mobile-profile-link">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                        <a href="{{ url_for('landing_page') }}" class="mobile-profile-link">
                            <i class="fas fa-cog"></i> Account Settings
                        </a>
                        <a href="{{ url_for('logout') }}" class="mobile-profile-link logout">
                            <i class="fas fa-sign-out-alt"></i> Log Out
                        </a>
                    </div>
                </div>
            </div>
            <div class="right-section">
                <div class="search-container">
                    <div class="search-bar">
                        <div class="search-dropdown">
                            <button class="search-dropdown-btn" onclick="toggleSearchDropdown()">
                                <span id="searchDropdownText">Geniuses</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="search-dropdown-menu" id="searchDropdownMenu">
                                <div class="search-dropdown-item" onclick="selectSearchType('Geniuses')">
                                    <i class="fas fa-user-graduate"></i>
                                    <span>Geniuses</span>
                                </div>
                                <div class="search-dropdown-item" onclick="selectSearchType('Jobs')">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Jobs</span>
                                </div>
                                <div class="search-dropdown-item" onclick="selectSearchType('Projects')">
                                    <i class="fas fa-project-diagram"></i>
                                    <span>Projects</span>
                                </div>
                            </div>
                        </div>
                        <input type="text" id="searchInput" placeholder="Search for Geniuses">
                        <i class="fas fa-search icon"></i>
                    </div>
                </div>
                <div class="auth-buttons">
                    <div class="notification-icon">
                        <i class="fas fa-bell"></i>
                        <span id="notification-count">0</span>
                    </div>
                    <div class="notification-dropdown">
                        <div class="notification-header">
                            <span>Notifications</span>
                            <span class="notification-header-actions">Mark all as read</span>
                        </div>
                        <div id="notification-list">
                            <!-- Notifications will be loaded here -->
                        </div>
                        <div id="empty-notifications" class="empty-notifications" style="display: none;">
                            <i class="far fa-bell-slash"></i>
                            <p>No notifications yet</p>
                        </div>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button">
                            <img src="{{ url_for('api_profile_photo', user_type='client', user_id=session.get('user_id')) }}" alt="Profile Picture">
                        </div>
                        <div class="profile-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-cog"></i> Account Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('logout') }}" class="logout-option">
                                <i class="fas fa-sign-out-alt"></i> Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Header Section -->
        <div class="page-header">
            <h1><i class="fas fa-file-contract"></i> All Contracts</h1>
            <p>Manage and track all your contracts in one place</p>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card active">
                <div class="icon">
                    <i class="fas fa-play-circle"></i>
                </div>
                <h3>12</h3>
                <p>Active Contracts</p>
            </div>
            <div class="stat-card completed">
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3>45</h3>
                <p>Completed Contracts</p>
            </div>
            <div class="stat-card pending">
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3>8</h3>
                <p>Pending Approval</p>
            </div>
            <div class="stat-card cancelled">
                <div class="icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <h3>3</h3>
                <p>Cancelled</p>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <div class="filters-grid">
                <div class="filter-group">
                    <label for="statusFilter">Status</label>
                    <select id="statusFilter" class="filter-select">
                        <option value="">All Statuses</option>
                        <option value="active">Active</option>
                        <option value="completed">Completed</option>
                        <option value="pending">Pending</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="clientFilter">Client</label>
                    <select id="clientFilter" class="filter-select">
                        <option value="">All Clients</option>
                        <option value="client1">TechCorp Inc.</option>
                        <option value="client2">Design Studio</option>
                        <option value="client3">StartupXYZ</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="dateFilter">Date Range</label>
                    <input type="date" id="dateFilter" class="filter-input">
                </div>
                <div class="filter-group">
                    <label for="searchFilter">Search</label>
                    <input type="text" id="searchFilter" class="filter-input" placeholder="Search contracts...">
                </div>
                <div class="filter-group">
                    <label>&nbsp;</label>
                    <button class="search-btn">
                        <i class="fas fa-search"></i> Filter
                    </button>
                </div>
            </div>
        </div>

        <!-- Contracts Section -->
        <div class="contracts-section">
            <div class="section-header">
                <h2 class="section-title">Contract List</h2>
            </div>

            <!-- Contract Cards -->
            <div class="contracts-list">
                <!-- Contract 1 - Active -->
                <div class="contract-card">
                    <div class="contract-header">
                        <div>
                            <div class="contract-title">Website Development Project</div>
                            <div class="contract-id">Contract #CT-2024-001</div>
                        </div>
                        <span class="status-badge active">Active</span>
                    </div>
                    <div class="contract-details">
                        <div class="detail-item">
                            <span class="detail-label">Client</span>
                            <span class="detail-value">TechCorp Inc.</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Value</span>
                            <span class="detail-value">$5,500</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Start Date</span>
                            <span class="detail-value">Jan 15, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">End Date</span>
                            <span class="detail-value">Mar 15, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Progress</span>
                            <span class="detail-value">65%</span>
                        </div>
                    </div>
                    <div class="contract-actions">
                        <button class="action-btn btn-primary">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-download"></i> Download
                        </button>
                    </div>
                </div>

                <!-- Contract 2 - Completed -->
                <div class="contract-card">
                    <div class="contract-header">
                        <div>
                            <div class="contract-title">Mobile App UI Design</div>
                            <div class="contract-id">Contract #CT-2024-002</div>
                        </div>
                        <span class="status-badge completed">Completed</span>
                    </div>
                    <div class="contract-details">
                        <div class="detail-item">
                            <span class="detail-label">Client</span>
                            <span class="detail-value">Design Studio</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Value</span>
                            <span class="detail-value">$3,200</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Start Date</span>
                            <span class="detail-value">Dec 1, 2023</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">End Date</span>
                            <span class="detail-value">Jan 10, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Progress</span>
                            <span class="detail-value">100%</span>
                        </div>
                    </div>
                    <div class="contract-actions">
                        <button class="action-btn btn-primary">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-download"></i> Download
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-star"></i> Rate
                        </button>
                    </div>
                </div>

                <!-- Contract 3 - Pending -->
                <div class="contract-card">
                    <div class="contract-header">
                        <div>
                            <div class="contract-title">Content Writing Package</div>
                            <div class="contract-id">Contract #CT-2024-003</div>
                        </div>
                        <span class="status-badge pending">Pending</span>
                    </div>
                    <div class="contract-details">
                        <div class="detail-item">
                            <span class="detail-label">Client</span>
                            <span class="detail-value">StartupXYZ</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Value</span>
                            <span class="detail-value">$1,800</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Start Date</span>
                            <span class="detail-value">Feb 1, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">End Date</span>
                            <span class="detail-value">Feb 28, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Progress</span>
                            <span class="detail-value">0%</span>
                        </div>
                    </div>
                    <div class="contract-actions">
                        <button class="action-btn btn-primary">
                            <i class="fas fa-check"></i> Accept
                        </button>
                        <button class="action-btn btn-danger">
                            <i class="fas fa-times"></i> Decline
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                    </div>
                </div>

                <!-- Contract 4 - Cancelled -->
                <div class="contract-card">
                    <div class="contract-header">
                        <div>
                            <div class="contract-title">E-commerce Platform Development</div>
                            <div class="contract-id">Contract #CT-2023-045</div>
                        </div>
                        <span class="status-badge cancelled">Cancelled</span>
                    </div>
                    <div class="contract-details">
                        <div class="detail-item">
                            <span class="detail-label">Client</span>
                            <span class="detail-value">RetailCorp</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Value</span>
                            <span class="detail-value">$8,500</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Start Date</span>
                            <span class="detail-value">Nov 15, 2023</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">End Date</span>
                            <span class="detail-value">Jan 30, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Progress</span>
                            <span class="detail-value">25%</span>
                        </div>
                    </div>
                    <div class="contract-actions">
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-archive"></i> Archive
                        </button>
                    </div>
                </div>
            </div>

            <!-- No Contracts State (hidden by default) -->
            <div class="no-contracts" style="display: none;">
                <i class="fas fa-file-contract"></i>
                <h3>No Contracts Found</h3>
                <p>You don't have any contracts yet. Start by applying to jobs or creating proposals.</p>
            </div>
        </div>
    </div>

    <script>
        // Filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const filterBtn = document.querySelector('.search-btn');
            const statusFilter = document.getElementById('statusFilter');
            const clientFilter = document.getElementById('clientFilter');
            const searchFilter = document.getElementById('searchFilter');
            const contractCards = document.querySelectorAll('.contract-card');

            function filterContracts() {
                const statusValue = statusFilter.value.toLowerCase();
                const clientValue = clientFilter.value.toLowerCase();
                const searchValue = searchFilter.value.toLowerCase();

                contractCards.forEach(card => {
                    const status = card.querySelector('.status-badge').textContent.toLowerCase();
                    const client = card.querySelector('.detail-value').textContent.toLowerCase();
                    const title = card.querySelector('.contract-title').textContent.toLowerCase();

                    const statusMatch = !statusValue || status.includes(statusValue);
                    const clientMatch = !clientValue || client.includes(clientValue);
                    const searchMatch = !searchValue || title.includes(searchValue) || client.includes(searchValue);

                    if (statusMatch && clientMatch && searchMatch) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            }

            filterBtn.addEventListener('click', filterContracts);
            searchFilter.addEventListener('input', filterContracts);
            statusFilter.addEventListener('change', filterContracts);
            clientFilter.addEventListener('change', filterContracts);

            // Action button handlers
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const action = this.textContent.trim();
                    const contractTitle = this.closest('.contract-card').querySelector('.contract-title').textContent;

                    // Add your action handlers here
                    console.log(`Action: ${action} on contract: ${contractTitle}`);

                    // Example: Show alert for demo
                    alert(`${action} clicked for: ${contractTitle}`);
                });
            });
        });
    </script>
</body>
</html>
