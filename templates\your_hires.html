<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Hires - GigGenius</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
            --neutral-900: #1f2937;
            --primary-gradient: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease-in-out;
        }

        html {
            scroll-behavior: smooth;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 2000px;
            margin: 0;
            padding: 0;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 4.5rem;
            position: relative;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }

        /* Mobile menu button */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--primary-blue);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            margin-right: 0.5rem;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .mobile-menu-btn:hover {
            color: var(--primary-pink);
            background-color: rgba(0, 74, 173, 0.05);
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
            padding-left: 0;
            flex: 1;
        }

        .logo {
            display: flex;
            align-items: center;
            color: var(--primary-pink);
        }

        .logo img {
            width: 3.5rem;
            height: 3.5rem;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-left: 0.5rem;
            margin-right: 0.5rem;
            color: var(--primary-pink);
        }

        .logo:hover, .logo:active {
            color: var(--primary-blue);
        }

        .logo:hover h1 {
            color: var(--primary-blue);
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
            height: 100%;
            margin: 0;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1rem;
            font-weight: 500;
            position: relative;
            white-space: nowrap;
        }

        .nav-links a:hover, .nav-links a.active {
            color: var(--primary-pink);
        }

        .nav-links a:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-pink);
            transition: width 0.3s ease;
        }

        .nav-links a:hover:after, .nav-links a.active:after {
            width: 100%;
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
            margin: 0;
        }

        .nav-dropbtn {
            font-weight: 500;
            font-size: 1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.3rem;
            position: relative;
            white-space: nowrap;
        }

        .nav-dropbtn:hover {
            color: var(--primary-pink);
        }

        .nav-dropbtn:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-pink);
            transition: width 0.3s ease;
        }

        .nav-dropbtn:hover:after {
            width: 100%;
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            top: 100%;
            left: 0;
            margin-top: 0.5rem;
        }

        .nav-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 0.9rem;
        }

        .nav-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .nav-dropdown-content a:after {
            display: none;
        }

        .nav-dropdown.active .nav-dropdown-content {
            display: block;
        }

        /* Right section container */
        .right-section {
            display: flex;
            align-items: center;
            gap: 2rem;
            padding-right: 0;
            height: 100%;
        }

        /* Search container */
        .search-container {
            display: flex;
            align-items: center;
            margin-right: 1.5rem;
        }

        @media (max-width: 992px) {
            .right-section {
                gap: 1rem;
            }

            .search-container {
                margin-right: 1rem;
            }
        }

        .search-bar {
            height: 40px;
            display: flex;
            align-items: center;
            background: #f5f7fa;
            border: 1px solid #e1e8ed;
            border-radius: 20px;
            width: 280px;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: 0 2px 8px rgba(0, 74, 173, 0.05);
            padding: 0 4px 0 6px;
        }

        .search-dropdown {
            position: relative;
            margin-right: 12px;
            border-right: 1px solid #e1e8ed;
            padding-right: 12px;
        }

        .search-dropdown-btn {
            background: white;
            border: 1px solid #d1dce5;
            border-radius: 15px;
            padding: 4px 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 500;
            color: #2c3e50;
            white-space: nowrap;
            height: 28px;
            transition: all 0.2s ease;
        }

        .search-dropdown-btn:hover {
            background: #f8f9fa;
            border-color: var(--primary-blue);
        }

        .search-dropdown-btn i {
            font-size: 10px;
            transition: transform 0.3s ease;
            color: #6c757d;
        }

        .search-dropdown.active .search-dropdown-btn i {
            transform: rotate(180deg);
        }

        .search-dropdown-menu {
            position: absolute;
            top: calc(100% + 5px);
            left: 0;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 74, 173, 0.15);
            z-index: 9999;
            min-width: 160px;
            display: none;
            overflow: hidden;
        }

        .search-dropdown-menu.show {
            display: block;
            animation: dropdownFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-8px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .search-dropdown-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 13px;
            font-weight: 500;
            color: #2c3e50;
            border-bottom: 1px solid #f1f5f9;
        }

        .search-dropdown-item:last-child {
            border-bottom: none;
        }

        .search-dropdown-item:hover {
            background: #f8f9fa;
            color: var(--primary-blue);
        }

        .search-dropdown-item i {
            width: 14px;
            height: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-blue);
            font-size: 12px;
        }

        .search-bar:focus-within {
            background: white;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
        }

        @media (max-width: 768px) {
            .search-bar {
                width: 180px;
            }
        }

        @media (max-width: 576px) {
            .search-bar {
                width: 150px;
            }
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 0.5rem 0 1rem;
            width: 100%;
            height: 100%;
            font-size: 0.9rem;
            background: transparent;
        }

        .search-bar .icon {
            color: #8a94a6;
            padding: 0 1rem 0 0.5rem;
            font-size: 0.9rem;
        }

        .search-bar:focus-within .icon {
            color: var(--primary-blue);
        }

        /* Auth buttons container */
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        /* Enhanced Notification Styles */
        .notification-icon {
            position: relative;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-icon:hover {
            background-color: rgba(0, 74, 173, 0.1);
        }

        .notification-icon i {
            font-size: 1.3rem;
            color: #4a5568;
        }

        #notification-count {
            position: absolute;
            top: 2px;
            right: 2px;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 50%;
            min-width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: 600;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .notification-dropdown {
            position: absolute;
            top: 60px;
            right: 10px;
            width: 380px;
            max-height: 500px;
            overflow-y: auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
            z-index: 1000;
            display: none;
            border: 1px solid rgba(0,0,0,0.08);
            animation: dropdown-fade 0.2s ease-out;
        }

        @media (max-width: 576px) {
            .notification-dropdown {
                width: calc(100vw - 40px);
                right: -100px;
                max-height: 400px;
            }
        }

        @media (max-width: 480px) {
            .notification-dropdown {
                right: -150px;
            }
        }

        @keyframes dropdown-fade {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .notification-dropdown.show {
            display: block;
        }

        .notification-header {
            padding: 18px 20px;
            border-bottom: 1px solid rgba(0,0,0,0.08);
            font-weight: 600;
            font-size: 1rem;
            color: var(--neutral-900);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-header-actions {
            font-size: 0.8rem;
            color: var(--primary-blue);
            cursor: pointer;
        }

        /* Profile dropdown */
        .profile-dropdown {
            position: relative;
            display: inline-block;
        }

        .profile-button {
            cursor: pointer;
            border-radius: 50%;
            overflow: hidden;
            width: 40px;
            height: 40px;
            border: 2px solid rgba(0, 74, 173, 0.1);
            transition: border-color 0.3s ease;
        }

        .profile-button:hover {
            border-color: var(--primary-blue);
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 60px;
            background-color: #fff;
            min-width: 220px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .profile-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .profile-dropdown-content a i {
            width: 20px;
            text-align: center;
        }

        .profile-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 8px 0;
        }

        .logout-option {
            color: #dc3545 !important;
        }

        .logout-option:hover {
            background-color: #fff5f5 !important;
            color: #dc3545 !important;
        }

        /* Show dropdown on click */
        .profile-dropdown.active .profile-dropdown-content {
            display: block;
        }

        /* Mobile Styles */
        .mobile-menu-header,
        .mobile-menu-content,
        .mobile-profile-section,
        .mobile-search-section,
        .mobile-profile-actions {
            display: none;
        }

        @media (max-width: 768px) {
            .navbar {
                padding: 0 0.5rem;
                height: 3.8rem;
            }

            .navbar-left {
                padding-left: 2.2rem;
            }

            .nav-links {
                display: none;
            }

            .mobile-menu-btn {
                display: flex;
                position: absolute;
                left: 0.4rem;
                top: 50%;
                transform: translateY(-50%);
                z-index: 1001;
            }

            .nav-links.active {
                display: flex;
                flex-direction: column;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
                z-index: 9999;
                padding: 0;
                margin: 0;
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
                scroll-behavior: smooth;
            }

            .mobile-menu-header,
            .mobile-menu-content,
            .mobile-profile-section,
            .mobile-search-section,
            .mobile-profile-actions {
                display: block;
            }

            .right-section {
                display: none !important;
            }
        }

        /* Page Header Section */
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--primary-gradient);
        }

        .page-header h1 {
            color: var(--primary-blue);
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-header h1 i {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
        }

        .page-header p {
            color: var(--text-gray);
            font-size: 16px;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        /* Overview Cards Styles - matching client_page.html */
        .stats-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin: 0 20px 30px 20px;
        }

        .stat-card {
            flex: 1;
            min-width: 180px;
            background-color: white;
            border-radius: 12px;
            padding: 1.2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            border: 1px solid #e0e4e8;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-blue), var(--primary-pink));
            opacity: 0.8;
            transition: width 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 74, 173, 0.08), 0 10px 20px rgba(205, 32, 139, 0.05);
            border-color: rgba(0, 74, 173, 0.3);
        }

        .stat-card:hover::before {
            width: 6px;
        }

        .stat-card.active {
            border-color: var(--primary-blue);
            background-color: #f9faff;
        }

        .stat-card.active::before {
            width: 6px;
            opacity: 1;
        }

        .card-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            border-radius: 10px;
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.1), rgba(205, 32, 139, 0.05));
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0, 74, 173, 0.08);
        }

        .stat-card:hover .card-icon {
            transform: scale(1.1);
            box-shadow: 0 6px 15px rgba(0, 74, 173, 0.12);
        }

        .stat-card:nth-child(even) .card-icon {
            background: linear-gradient(135deg, rgba(205, 32, 139, 0.05), rgba(0, 74, 173, 0.1));
        }

        .card-icon i {
            font-size: 1.3rem;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
        }

        .stat-card:nth-child(even) .card-icon i {
            background: linear-gradient(135deg, var(--primary-pink), var(--primary-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
        }

        .card-content {
            flex: 1;
        }

        .stat-card h3 {
            font-size: 1.1rem;
            font-weight: 700;
            color: #333;
            margin: 0;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: color 0.3s ease;
        }

        .stat-card:hover h3 {
            color: var(--primary-blue);
        }

        .stat-card:nth-child(even):hover h3 {
            color: var(--primary-pink);
        }

        .stat-card p {
            font-size: 0.9rem;
            font-weight: 600;
            color: #444;
            margin: 0.3rem 0 0 0;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: color 0.3s ease;
        }

        .stat-card:hover p {
            color: var(--primary-blue);
        }

        .stat-card:nth-child(even):hover p {
            color: var(--primary-pink);
        }

        /* Filters Section */
        .filters-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 0 20px 30px 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            border: 1px solid #e0e4e8;
            position: relative;
            overflow: hidden;
        }

        .filters-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: var(--primary-gradient);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            align-items: end;
        }

        .filter-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--primary-blue);
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .filter-select {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e0e4e8;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            transition: all 0.3s ease;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            color: #333;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
            background-color: #fafcff;
        }

        .filter-select:hover {
            border-color: rgba(0, 74, 173, 0.3);
        }

        .search-btn {
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(0, 74, 173, 0.2);
            position: relative;
            overflow: hidden;
        }

        .search-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.4), rgba(255,255,255,0.1));
            transition: all 0.6s ease;
        }

        .search-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 74, 173, 0.3);
        }

        .search-btn:hover::before {
            left: 100%;
        }

        .search-btn:active {
            transform: translateY(1px);
            box-shadow: 0 4px 8px rgba(0, 74, 173, 0.3);
        }

        /* Hires Section */
        .hires-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin: 0 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f5f9;
        }

        .section-header h2 {
            color: #2d3748;
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .view-options {
            display: flex;
            gap: 8px;
        }

        .view-btn {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #6c757d;
        }

        .view-btn:hover {
            background: #e9ecef;
            color: var(--primary-blue);
        }

        .view-btn.active {
            background: var(--primary-blue);
            color: white;
            border-color: var(--primary-blue);
        }

        /* Hires Grid */
        .hires-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
        }

        .hire-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .hire-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: var(--primary-blue);
        }

        .hire-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .genius-info {
            display: flex;
            gap: 15px;
            flex: 1;
        }

        .genius-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #f1f5f9;
        }

        .genius-details h3 {
            color: #2d3748;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .genius-title {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .genius-rating {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .stars {
            color: #fbbf24;
            font-size: 14px;
        }

        .rating-text {
            color: #6b7280;
            font-size: 12px;
        }

        .hire-status {
            flex-shrink: 0;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-badge.active {
            background: #dcfce7;
            color: #166534;
        }

        .status-badge.completed {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-badge.paused {
            background: #fef3c7;
            color: #92400e;
        }

        .status-badge.cancelled {
            background: #fee2e2;
            color: #dc2626;
        }

        /* Project Info */
        .project-info {
            margin-bottom: 20px;
        }

        .project-info h4 {
            color: #2d3748;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .project-description {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .project-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6b7280;
            font-size: 13px;
        }

        .detail-item i {
            color: var(--primary-blue);
            width: 14px;
        }

        /* Progress Section */
        .progress-section {
            margin-bottom: 15px;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .progress-header span:first-child {
            color: #374151;
            font-size: 14px;
            font-weight: 500;
        }

        .progress-percentage {
            color: var(--primary-blue);
            font-size: 14px;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f1f5f9;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-blue), var(--primary-pink));
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-fill.completed {
            background: linear-gradient(90deg, #10b981, #059669);
        }

        /* Hire Actions */
        .hire-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            flex: 1;
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .action-btn.primary {
            background: var(--primary-blue);
            color: white;
        }

        .action-btn.primary:hover {
            background: var(--primary-pink);
            transform: translateY(-1px);
        }

        .action-btn.secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #e9ecef;
        }

        .action-btn.secondary:hover {
            background: #e9ecef;
            color: var(--primary-blue);
        }

        .action-btn.menu {
            flex: 0 0 auto;
            width: 40px;
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #e9ecef;
        }

        .action-btn.menu:hover {
            background: #e9ecef;
            color: var(--primary-blue);
        }

        /* Empty State */
        .empty-state {
            grid-column: 1 / -1;
            text-align: center;
            padding: 60px 20px;
        }

        .empty-content {
            max-width: 400px;
            margin: 0 auto;
        }

        .empty-icon {
            font-size: 64px;
            color: #d1d5db;
            margin-bottom: 20px;
        }

        .empty-content h3 {
            color: #374151;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .empty-content p {
            color: #6b7280;
            margin-bottom: 20px;
        }

        .clear-filters-btn {
            background: var(--primary-blue);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .clear-filters-btn:hover {
            background: var(--primary-pink);
            transform: translateY(-2px);
        }

        /* List View */
        .hires-grid.list-view {
            grid-template-columns: 1fr;
        }

        .hires-grid.list-view .hire-card {
            display: flex;
            align-items: center;
            padding: 15px 20px;
        }

        .hires-grid.list-view .hire-header {
            margin-bottom: 0;
            margin-right: 20px;
            min-width: 300px;
        }

        .hires-grid.list-view .project-info {
            flex: 1;
            margin-bottom: 0;
            margin-right: 20px;
        }

        .hires-grid.list-view .hire-actions {
            flex-shrink: 0;
            min-width: 200px;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .page-header {
                margin: 10px;
                padding: 20px;
            }

            .page-header h1 {
                font-size: 24px;
            }

            .stats-grid {
                margin: 0 10px 20px 10px;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .filters-section {
                margin: 0 10px 20px 10px;
            }

            .hires-section {
                margin: 0 10px;
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .hires-grid {
                grid-template-columns: 1fr;
            }

            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .project-details {
                grid-template-columns: 1fr;
            }

            .hire-actions {
                flex-direction: column;
            }

            .hires-grid.list-view .hire-card {
                flex-direction: column;
                align-items: flex-start;
            }

            .hires-grid.list-view .hire-header,
            .hires-grid.list-view .project-info {
                margin-right: 0;
                margin-bottom: 15px;
                min-width: auto;
                width: 100%;
            }

            .hires-grid.list-view .hire-actions {
                width: 100%;
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Navbar -->
        <nav class="navbar">
            <div class="navbar-left">
                <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                    <div class="logo">
                        <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                        <h1>GigGenius</h1>
                    </div>
                </a>
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="nav-links" id="navLinks">
                    <!-- Desktop Navigation Links -->
                    <a href="{{ url_for('page1') }}">Post a Gig</a>

                    <!-- Desktop Overview Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Overview
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('allgigpost') }}">All gig posts</a>
                            <a href="{{ url_for('all_contracts') }}">All contracts</a>
                            <a href="{{ url_for('your_hires') }}">Your Hires</a>
                        </div>
                    </div>

                    <!-- Desktop Manage Work Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Manage Work
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Timesheet</a>
                            <a href="{{ url_for('landing_page') }}">Invoices</a>
                        </div>
                    </div>

                    <!-- Desktop Reports Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Reports
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Weekly Summary</a>
                            <a href="{{ url_for('landing_page') }}">Transaction History</a>
                        </div>
                    </div>

                    <a href="{{ url_for('messages') }}">Messages</a>

                    <!-- Mobile Profile Actions (only visible on mobile) - At Bottom -->
                    <div class="mobile-profile-actions">
                        <a href="{{ url_for('landing_page') }}" class="mobile-profile-link">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                        <a href="{{ url_for('landing_page') }}" class="mobile-profile-link">
                            <i class="fas fa-cog"></i> Account Settings
                        </a>
                        <a href="{{ url_for('logout') }}" class="mobile-profile-link logout">
                            <i class="fas fa-sign-out-alt"></i> Log Out
                        </a>
                    </div>
                </div>
            </div>
            <div class="right-section">
                <div class="search-container">
                    <div class="search-bar">
                        <div class="search-dropdown">
                            <button class="search-dropdown-btn" onclick="toggleSearchDropdown()">
                                <span id="searchDropdownText">Geniuses</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="search-dropdown-menu" id="searchDropdownMenu">
                                <div class="search-dropdown-item" onclick="selectSearchType('Geniuses')">
                                    <i class="fas fa-user-graduate"></i>
                                    <span>Geniuses</span>
                                </div>
                                <div class="search-dropdown-item" onclick="selectSearchType('Jobs')">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Jobs</span>
                                </div>
                                <div class="search-dropdown-item" onclick="selectSearchType('Projects')">
                                    <i class="fas fa-project-diagram"></i>
                                    <span>Projects</span>
                                </div>
                            </div>
                        </div>
                        <input type="text" id="searchInput" placeholder="Search for Geniuses">
                        <i class="fas fa-search icon"></i>
                    </div>
                </div>
                <div class="auth-buttons">
                    <div class="notification-icon">
                        <i class="fas fa-bell"></i>
                        <span id="notification-count">0</span>
                    </div>
                    <div class="notification-dropdown">
                        <div class="notification-header">
                            <span>Notifications</span>
                            <span class="notification-header-actions">Mark all as read</span>
                        </div>
                        <div id="notification-list">
                            <!-- Notifications will be loaded here -->
                        </div>
                        <div id="empty-notifications" class="empty-notifications" style="display: none;">
                            <i class="far fa-bell-slash"></i>
                            <p>No notifications yet</p>
                        </div>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button">
                            <img src="{{ url_for('api_profile_photo', user_type='client', user_id=session.get('user_id')) }}" alt="Profile Picture">
                        </div>
                        <div class="profile-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-cog"></i> Account Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('logout') }}" class="logout-option">
                                <i class="fas fa-sign-out-alt"></i> Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Header Section -->
        <div class="page-header">
            <h1><i class="fas fa-users"></i> Your Hires</h1>
            <p>Manage and track all your hired geniuses and their work progress</p>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card active">
                <div class="card-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="card-content">
                    <h3>12</h3>
                    <p>Total Hires</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="card-icon">
                    <i class="fas fa-briefcase"></i>
                </div>
                <div class="card-content">
                    <h3>8</h3>
                    <p>Active Projects</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="card-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="card-content">
                    <h3>4</h3>
                    <p>Completed Projects</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="card-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="card-content">
                    <h3>$15,240</h3>
                    <p>Total Spent</p>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <div class="filters-grid">
                <div class="filter-group">
                    <label for="statusFilter">Project Status</label>
                    <select id="statusFilter" class="filter-select">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="completed">Completed</option>
                        <option value="paused">Paused</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="skillFilter">Skills</label>
                    <select id="skillFilter" class="filter-select">
                        <option value="">All Skills</option>
                        <option value="web-development">Web Development</option>
                        <option value="mobile-development">Mobile Development</option>
                        <option value="ui-ux-design">UI/UX Design</option>
                        <option value="data-science">Data Science</option>
                        <option value="digital-marketing">Digital Marketing</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="searchFilter">Search</label>
                    <input type="text" id="searchFilter" class="filter-select" placeholder="Search by name or project...">
                </div>
                <div class="filter-group">
                    <button class="search-btn">
                        <i class="fas fa-search"></i> Filter
                    </button>
                </div>
            </div>
        </div>

        <!-- Hires Section -->
        <div class="hires-section">
            <div class="section-header">
                <h2><i class="fas fa-briefcase"></i> Your Hired Geniuses</h2>
                <div class="view-options">
                    <button class="view-btn grid active" data-tooltip="Grid View">
                        <i class="fas fa-th-large"></i>
                    </button>
                    <button class="view-btn list" data-tooltip="List View">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>

            <!-- Hires Grid -->
            <div class="hires-grid" id="hiresGrid">
                <!-- Hire Card 1 -->
                <div class="hire-card" data-status="active" data-skill="web-development">
                    <div class="hire-header">
                        <div class="genius-info">
                            <img src="https://via.placeholder.com/60x60" alt="Profile Picture" class="genius-avatar">
                            <div class="genius-details">
                                <h3>Sarah Johnson</h3>
                                <p class="genius-title">Full Stack Developer</p>
                                <div class="genius-rating">
                                    <span class="stars">★★★★★</span>
                                    <span class="rating-text">5.0 (24 reviews)</span>
                                </div>
                            </div>
                        </div>
                        <div class="hire-status">
                            <span class="status-badge active">Active</span>
                        </div>
                    </div>

                    <div class="project-info">
                        <h4>E-commerce Website Development</h4>
                        <p class="project-description">Building a modern e-commerce platform with React and Node.js</p>

                        <div class="project-details">
                            <div class="detail-item">
                                <i class="fas fa-calendar"></i>
                                <span>Started: Jan 15, 2024</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span>Deadline: Mar 15, 2024</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-dollar-sign"></i>
                                <span>Budget: $3,500</span>
                            </div>
                        </div>

                        <div class="progress-section">
                            <div class="progress-header">
                                <span>Progress</span>
                                <span class="progress-percentage">75%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 75%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="hire-actions">
                        <button class="action-btn primary">
                            <i class="fas fa-comments"></i> Message
                        </button>
                        <button class="action-btn secondary">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                        <button class="action-btn menu">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                </div>

                <!-- Hire Card 2 -->
                <div class="hire-card" data-status="completed" data-skill="ui-ux-design">
                    <div class="hire-header">
                        <div class="genius-info">
                            <img src="https://via.placeholder.com/60x60" alt="Profile Picture" class="genius-avatar">
                            <div class="genius-details">
                                <h3>Michael Chen</h3>
                                <p class="genius-title">UI/UX Designer</p>
                                <div class="genius-rating">
                                    <span class="stars">★★★★☆</span>
                                    <span class="rating-text">4.8 (18 reviews)</span>
                                </div>
                            </div>
                        </div>
                        <div class="hire-status">
                            <span class="status-badge completed">Completed</span>
                        </div>
                    </div>

                    <div class="project-info">
                        <h4>Mobile App UI Design</h4>
                        <p class="project-description">Complete UI/UX design for iOS and Android mobile application</p>

                        <div class="project-details">
                            <div class="detail-item">
                                <i class="fas fa-calendar"></i>
                                <span>Started: Dec 1, 2023</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Completed: Jan 10, 2024</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-dollar-sign"></i>
                                <span>Budget: $2,200</span>
                            </div>
                        </div>

                        <div class="progress-section">
                            <div class="progress-header">
                                <span>Progress</span>
                                <span class="progress-percentage">100%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill completed" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="hire-actions">
                        <button class="action-btn primary">
                            <i class="fas fa-star"></i> Rate & Review
                        </button>
                        <button class="action-btn secondary">
                            <i class="fas fa-download"></i> Download Files
                        </button>
                        <button class="action-btn menu">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                </div>

                <!-- Hire Card 3 -->
                <div class="hire-card" data-status="active" data-skill="mobile-development">
                    <div class="hire-header">
                        <div class="genius-info">
                            <img src="https://via.placeholder.com/60x60" alt="Profile Picture" class="genius-avatar">
                            <div class="genius-details">
                                <h3>Emily Rodriguez</h3>
                                <p class="genius-title">Mobile Developer</p>
                                <div class="genius-rating">
                                    <span class="stars">★★★★★</span>
                                    <span class="rating-text">4.9 (31 reviews)</span>
                                </div>
                            </div>
                        </div>
                        <div class="hire-status">
                            <span class="status-badge active">Active</span>
                        </div>
                    </div>

                    <div class="project-info">
                        <h4>React Native App Development</h4>
                        <p class="project-description">Cross-platform mobile app development using React Native</p>

                        <div class="project-details">
                            <div class="detail-item">
                                <i class="fas fa-calendar"></i>
                                <span>Started: Jan 20, 2024</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span>Deadline: Apr 20, 2024</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-dollar-sign"></i>
                                <span>Budget: $4,800</span>
                            </div>
                        </div>

                        <div class="progress-section">
                            <div class="progress-header">
                                <span>Progress</span>
                                <span class="progress-percentage">45%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 45%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="hire-actions">
                        <button class="action-btn primary">
                            <i class="fas fa-comments"></i> Message
                        </button>
                        <button class="action-btn secondary">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                        <button class="action-btn menu">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                </div>

                <!-- Empty State (hidden by default) -->
                <div class="empty-state" id="emptyState" style="display: none;">
                    <div class="empty-content">
                        <i class="fas fa-users empty-icon"></i>
                        <h3>No hires found</h3>
                        <p>Try adjusting your filters or search criteria</p>
                        <button class="clear-filters-btn" onclick="clearFilters()">
                            <i class="fas fa-refresh"></i> Clear Filters
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Navigation dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const mobileCloseBtn = document.getElementById('mobileCloseBtn');
            const navLinks = document.getElementById('navLinks');

            function openMobileMenu() {
                navLinks.classList.add('active');
            }

            function closeMobileMenu() {
                navLinks.classList.remove('active');
            }

            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function() {
                    openMobileMenu();
                });
            }

            if (mobileCloseBtn) {
                mobileCloseBtn.addEventListener('click', function() {
                    closeMobileMenu();
                });
            }

            // Navigation dropdowns
            const navDropdowns = document.querySelectorAll('.nav-dropdown');

            navDropdowns.forEach(dropdown => {
                const dropBtn = dropdown.querySelector('.nav-dropbtn');

                dropBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Toggle current dropdown
                    dropdown.classList.toggle('active');

                    // Close other dropdowns
                    navDropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown) {
                            otherDropdown.classList.remove('active');
                            const otherIcon = otherDropdown.querySelector('.nav-dropbtn i');
                            if (otherIcon) {
                                otherIcon.classList.remove('fa-chevron-up');
                                otherIcon.classList.add('fa-chevron-down');
                            }
                        }
                    });

                    // Change dropdown icon
                    const icon = this.querySelector('i');
                    if (dropdown.classList.contains('active')) {
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-up');
                    } else {
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    }
                });
            });

            // Close navigation dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                const navDropdowns = document.querySelectorAll('.nav-dropdown');
                let clickedInsideDropdown = false;

                navDropdowns.forEach(dropdown => {
                    if (dropdown.contains(e.target)) {
                        clickedInsideDropdown = true;
                    }
                });

                if (!clickedInsideDropdown) {
                    navDropdowns.forEach(dropdown => {
                        dropdown.classList.remove('active');
                        const icon = dropdown.querySelector('.nav-dropbtn i');
                        if (icon) {
                            icon.classList.remove('fa-chevron-up');
                            icon.classList.add('fa-chevron-down');
                        }
                    });
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 992 && navLinks.classList.contains('active')) {
                    closeMobileMenu();

                    // Reset all dropdowns
                    navDropdowns.forEach(dropdown => {
                        dropdown.classList.remove('active');
                        const icon = dropdown.querySelector('.nav-dropbtn i');
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    });
                }
            });
        });

        // Search dropdown functions
        function toggleSearchDropdown() {
            const menu = document.getElementById('searchDropdownMenu');
            const dropdown = document.querySelector('.search-dropdown');

            if (menu.classList.contains('show')) {
                menu.classList.remove('show');
                dropdown.classList.remove('active');
            } else {
                menu.classList.add('show');
                dropdown.classList.add('active');
            }
        }

        function selectSearchType(type) {
            document.getElementById('searchDropdownText').textContent = type;
            document.getElementById('searchDropdownMenu').classList.remove('show');
            document.querySelector('.search-dropdown').classList.remove('active');

            // Update search placeholder
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.placeholder = 'Search for ' + type;
            }
        }

        // Close search dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const searchDropdown = document.querySelector('.search-dropdown');
            const searchMenu = document.getElementById('searchDropdownMenu');

            if (searchDropdown && !searchDropdown.contains(event.target)) {
                searchMenu.classList.remove('show');
                searchDropdown.classList.remove('active');
            }
        });

        // Notification dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const notificationIcon = document.querySelector('.notification-icon');
            const dropdown = document.querySelector('.notification-dropdown');

            notificationIcon.addEventListener('click', function(e) {
                e.stopPropagation();
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            });

            document.addEventListener('click', function(e) {
                if (!notificationIcon.contains(e.target)) {
                    dropdown.style.display = 'none';
                }
            });

            // Mark all as read
            const markAllBtn = document.querySelector('.notification-header-actions');
            if (markAllBtn) {
                markAllBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    document.querySelectorAll('.notification-item.unread').forEach(item => {
                        item.classList.remove('unread');
                    });

                    document.querySelector('#notification-count').textContent = '0';
                });
            }
        });

        // Profile dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const profileDropdown = document.querySelector('.profile-dropdown');
            const profileButton = profileDropdown.querySelector('.profile-button');

            // Toggle dropdown on profile button click
            profileButton.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdown.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdown.classList.remove('active');
                }
            });
        });

        // View toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const viewBtns = document.querySelectorAll('.view-btn');
            const hiresGrid = document.getElementById('hiresGrid');

            viewBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // Remove active class from all buttons
                    viewBtns.forEach(b => b.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Toggle grid view
                    if (this.classList.contains('list')) {
                        hiresGrid.classList.add('list-view');
                    } else {
                        hiresGrid.classList.remove('list-view');
                    }
                });
            });
        });

        // Filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const filterBtn = document.querySelector('.search-btn');
            const statusFilter = document.getElementById('statusFilter');
            const skillFilter = document.getElementById('skillFilter');
            const searchFilter = document.getElementById('searchFilter');
            const hireCards = document.querySelectorAll('.hire-card');
            const emptyState = document.getElementById('emptyState');

            function filterHires() {
                const statusValue = statusFilter.value.toLowerCase();
                const skillValue = skillFilter.value.toLowerCase();
                const searchValue = searchFilter.value.toLowerCase();
                let visibleCards = 0;

                hireCards.forEach(card => {
                    const status = card.getAttribute('data-status').toLowerCase();
                    const skill = card.getAttribute('data-skill').toLowerCase();
                    const geniusName = card.querySelector('.genius-details h3').textContent.toLowerCase();
                    const projectTitle = card.querySelector('.project-info h4').textContent.toLowerCase();

                    const statusMatch = !statusValue || status === statusValue;
                    const skillMatch = !skillValue || skill === skillValue;
                    const searchMatch = !searchValue ||
                        geniusName.includes(searchValue) ||
                        projectTitle.includes(searchValue);

                    if (statusMatch && skillMatch && searchMatch) {
                        card.style.display = 'block';
                        visibleCards++;
                    } else {
                        card.style.display = 'none';
                    }
                });

                // Show/hide empty state
                if (visibleCards === 0) {
                    emptyState.style.display = 'block';
                } else {
                    emptyState.style.display = 'none';
                }
            }

            // Add event listeners
            filterBtn.addEventListener('click', filterHires);
            searchFilter.addEventListener('input', filterHires);
            statusFilter.addEventListener('change', filterHires);
            skillFilter.addEventListener('change', filterHires);

            // Action button handlers
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const action = this.textContent.trim();
                    const geniusName = this.closest('.hire-card').querySelector('.genius-details h3').textContent;

                    // Add your action handlers here
                    console.log(`Action: ${action} for genius: ${geniusName}`);

                    // Example: Show alert for demo
                    if (action.includes('Message')) {
                        alert(`Opening chat with ${geniusName}`);
                    } else if (action.includes('View Details')) {
                        alert(`Viewing details for ${geniusName}'s project`);
                    } else if (action.includes('Rate & Review')) {
                        alert(`Opening rating form for ${geniusName}`);
                    } else if (action.includes('Download Files')) {
                        alert(`Downloading files from ${geniusName}`);
                    }
                });
            });
        });

        // Clear filters function
        function clearFilters() {
            document.getElementById('statusFilter').value = '';
            document.getElementById('skillFilter').value = '';
            document.getElementById('searchFilter').value = '';

            // Show all cards
            document.querySelectorAll('.hire-card').forEach(card => {
                card.style.display = 'block';
            });

            // Hide empty state
            document.getElementById('emptyState').style.display = 'none';
        }

        // Tooltip functionality for view buttons
        document.addEventListener('DOMContentLoaded', function() {
            const tooltipElements = document.querySelectorAll('[data-tooltip]');

            tooltipElements.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    const tooltip = document.createElement('div');
                    tooltip.className = 'tooltip';
                    tooltip.textContent = this.getAttribute('data-tooltip');
                    tooltip.style.cssText = `
                        position: absolute;
                        background: #333;
                        color: white;
                        padding: 5px 10px;
                        border-radius: 4px;
                        font-size: 12px;
                        z-index: 1000;
                        pointer-events: none;
                        white-space: nowrap;
                    `;

                    document.body.appendChild(tooltip);

                    const rect = this.getBoundingClientRect();
                    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
                    tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';

                    this._tooltip = tooltip;
                });

                element.addEventListener('mouseleave', function() {
                    if (this._tooltip) {
                        document.body.removeChild(this._tooltip);
                        this._tooltip = null;
                    }
                });
            });
        });
    </script>
</body>
</html>
